import React, { useRef } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, Alert } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import PaperOnboarding, {
  PaperOnboardingItemType,
  PaperOnboardingMethods,
} from '@gorhom/paper-onboarding';

// Demo data for the onboarding screens
const onboardingData: PaperOnboardingItemType[] = [
  {
    title: 'Welcome to Paper Onboarding',
    description:
      'Experience smooth animations powered by Reanimated v3 and modern gesture handling',
    backgroundColor: '#667eea',
    image: () => (
      <View style={styles.imageContainer}>
        <Text style={styles.emoji}>🎉</Text>
        <Text style={styles.imageText}>Expo SDK 53</Text>
      </View>
    ),
    icon: () => <Text style={styles.iconText}>🚀</Text>,
  },
  {
    title: 'Smooth Animations',
    description:
      'All animations run on the UI thread for buttery smooth 60fps performance',
    backgroundColor: '#764ba2',
    image: () => (
      <View style={styles.imageContainer}>
        <Text style={styles.emoji}>⚡</Text>
        <Text style={styles.imageText}>60 FPS</Text>
      </View>
    ),
    icon: () => <Text style={styles.iconText}>⚡</Text>,
  },
  {
    title: 'Modern Gestures',
    description:
      'Powered by react-native-gesture-handler v2 for responsive touch interactions',
    backgroundColor: '#f093fb',
    image: () => (
      <View style={styles.imageContainer}>
        <Text style={styles.emoji}>👆</Text>
        <Text style={styles.imageText}>Touch & Swipe</Text>
      </View>
    ),
    icon: () => <Text style={styles.iconText}>👆</Text>,
  },
  {
    title: 'TypeScript Ready',
    description:
      'Full TypeScript support with modern SharedValue types and excellent developer experience',
    backgroundColor: '#4facfe',
    image: () => (
      <View style={styles.imageContainer}>
        <Text style={styles.emoji}>🛡️</Text>
        <Text style={styles.imageText}>Type Safe</Text>
      </View>
    ),
    icon: () => <Text style={styles.iconText}>🛡️</Text>,
  },
  {
    title: 'Production Ready',
    description:
      'Successfully migrated to Expo SDK 53 with no breaking changes. Ready for your next project!',
    backgroundColor: '#43e97b',
    image: () => (
      <View style={styles.imageContainer}>
        <Text style={styles.emoji}>✅</Text>
        <Text style={styles.imageText}>Ready!</Text>
      </View>
    ),
    icon: () => <Text style={styles.iconText}>✅</Text>,
    showCloseButton: true,
  },
];

export default function App() {
  const safeInsets = useSafeAreaInsets();
  const onboardingRef = useRef<PaperOnboardingMethods>(null);

  const handleIndexChange = (index: number) => {
    console.log('Current page index:', index);
  };

  const handleClosePress = () => {
    Alert.alert(
      'Demo Complete! 🎉',
      'The Paper Onboarding library has been successfully migrated to Expo SDK 53!\n\n✅ Reanimated v3\n✅ Gesture Handler v2\n✅ Modern TypeScript\n✅ UI Thread Animations',
      [
        {
          text: 'Restart Demo',
          onPress: () => {
            // Navigate back to first page
            onboardingRef.current?.previous();
            onboardingRef.current?.previous();
            onboardingRef.current?.previous();
            onboardingRef.current?.previous();
          },
        },
        { text: 'OK', style: 'default' },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      <PaperOnboarding
        ref={onboardingRef}
        data={onboardingData}
        safeInsets={safeInsets}
        onIndexChange={handleIndexChange}
        onCloseButtonPress={handleClosePress}
        titleStyle={styles.title}
        descriptionStyle={styles.description}
        closeButtonText="Complete Demo"
        closeButtonTextStyle={styles.closeButtonText}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  emoji: {
    fontSize: 80,
    marginBottom: 10,
  },
  imageText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
  iconText: {
    fontSize: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  closeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});
